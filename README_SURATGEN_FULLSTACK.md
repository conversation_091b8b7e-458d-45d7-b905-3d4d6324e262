# 📄 Surat Generator Kemenag — Fullstack App (Laravel + React + shadcn/ui)

Aplikasi ini digunakan untuk **meng-generate surat otomatis** dari file template Word (`.docx`) berdasarkan data yang diisi pengguna. Digunakan oleh Kementerian Agama (Kabupaten/Kota, Kanwil, dan P<PERSON>t).

---

## ⚙️ Stack Teknologi

- **Backend**: Laravel 12, PHP 8.2, PhpWord
- **Frontend**: React + Vite + TailwindCSS + shadcn/ui
- **Komunikasi**: REST API (axios)
- **Template Engine**: TemplateProcessor (`phpoffice/phpword`)

---

## ✅ Fitur Utama

### 🖇 Backend Laravel
- [x] Upload dan simpan file .docx di `storage/app/templates/`
- [x] Ambil daftar template dan placeholder
- [x] Generate file surat `.docx` dari data JSON
- [x] Simpan hasil generate di `storage/app/generated/`
- [x] Download file hasil generate

### 🧩 Frontend React
- [x] Ambil list template dari API
- [x] Ambil placeholder dan buat form dinamis
- [x] Kirim data ke backend dan download hasil surat
- [x] UI clean dan responsive (shadcn/ui + TailwindCSS)

---

## 📁 Struktur Folder

### Laravel
```
storage/
├── app/
│   ├── templates/    ← Simpan template .docx
│   └── generated/    ← Hasil file .docx yang digenerate
```

### React
```
src/
├── components/
│   ├── TemplateList.jsx
│   ├── TemplateForm.jsx
├── pages/
│   └── Dashboard.jsx
├── api/
│   └── suratApi.js
```

---

## 🧠 Alur Aplikasi

1. User frontend memilih template surat
2. Frontend fetch placeholder dari backend
3. Form ditampilkan otomatis sesuai placeholder
4. User isi data → kirim ke backend (POST)
5. Backend isi .docx dan kirim file hasil
6. Frontend unduh otomatis hasil surat

---

## 📥 Contoh JSON Input ke Endpoint Laravel

```json
{
  "kabkota": "Kota Bima",
  "jln": "Jl. Soekarno Hatta No.1",
  "telfon": "0374-123456",
  "fax": "0374-654321",
  "email": "<EMAIL>",
  "namapejabat": "Drs. H. Zainuddin, M.Ag",
  "nippejabat": "19651231 199003 1 001",
  "pangkatgolpejabat": "Pembina / IV.a",
  "jabatanpejabat": "Kepala Kantor",
  "namapegawai": "Rival Biasrori",
  "nippegawai": "19880404 201012 1 002",
  "pangkatgolpegawai": "Penata / III.c",
  "jabatanpegawai": "Analis SDM",
  "tempattugas": "Kemenag Kota Bima",
  "kabataukotatujuan": "Kabupaten Lombok Barat",
  "jabatnpegawai": "Analis Kepegawaian",
  "dd-mm-yyyy": "18 Juli 2025"
}
```

---

## 🚀 Endpoint API Laravel

- `GET /api/templates` → Daftar template .docx
- `GET /api/templates/{id}` → Placeholder yang harus diisi
- `POST /api/templates/{id}/generate` → Kirim data JSON → download file surat

---

## 📦 Instalasi Backend Laravel

```bash
composer create-project laravel/laravel suratgen
cd suratgen
composer require phpoffice/phpword
php artisan serve
```

> Simpan template di: `storage/app/templates/`

---

## ⚛ Instalasi Frontend React

```bash
npm create vite@latest suratgen-frontend --template react
cd suratgen-frontend
npm install
npm install axios clsx tailwindcss shadcn-ui
npx tailwindcss init -p
```

> Gunakan komponen dinamis dari `shadcn/ui` untuk tampilan form

---

## 🧪 Tips Tambahan

- Pastikan file `.docx` tidak ada spasi atau karakter aneh
- Jalankan `php artisan config:clear` jika file tidak terbaca
- Cek placeholder pakai: `zip://path#word/document.xml`

---

## 👨‍💻 Kontributor
- Backend: Laravel, PhpWord
- Frontend: React + Tailwind + shadcn/ui
- Tools: Postman, VSCode, Tinker

---

## 🔐 Autentikasi & Multi User (Next Step)
- [ ] Role: kab/kota, kanwil, pusat
- [ ] Login, dashboard per role
- [ ] Verifikasi dan riwayat surat