import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { suratApi } from '../api/suratApi';
import { ArrowLeft, Download, Loader2 } from 'lucide-react';

const TemplateForm = ({ template, onBack }) => {
    const [formData, setFormData] = useState({});
    const [loading, setLoading] = useState(false);
    const [templateData, setTemplateData] = useState(null);
    const [error, setError] = useState(null);

    useEffect(() => {
        fetchTemplateDetails();
    }, [template.id]);

    const fetchTemplateDetails = async () => {
        try {
            setLoading(true);
            const response = await suratApi.getTemplate(template.id);
            setTemplateData(response.data);
            
            // Initialize form data with empty values
            const initialData = {};
            if (response.data.placeholders) {
                response.data.placeholders.forEach(placeholder => {
                    initialData[placeholder] = '';
                });
            }
            setFormData(initialData);
        } catch (err) {
            setError('Gagal memuat detail template: ' + (err.message || 'Unknown error'));
        } finally {
            setLoading(false);
        }
    };

    const handleInputChange = (placeholder, value) => {
        setFormData(prev => ({
            ...prev,
            [placeholder]: value
        }));
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        
        try {
            setLoading(true);
            await suratApi.generateDocument(template.id, formData);
            // File will be automatically downloaded
        } catch (err) {
            setError('Gagal generate dokumen: ' + (err.message || 'Unknown error'));
        } finally {
            setLoading(false);
        }
    };

    const getFieldLabel = (placeholder) => {
        const labelMap = {
            'kabkota': 'Kabupaten/Kota',
            'jln': 'Alamat Jalan',
            'telfon': 'Telepon',
            'fax': 'Fax',
            'email': 'Email',
            'namapejabat': 'Nama Pejabat',
            'nippejabat': 'NIP Pejabat',
            'pangkatgolpejabat': 'Pangkat/Golongan Pejabat',
            'jabatanpejabat': 'Jabatan Pejabat',
            'namapegawai': 'Nama Pegawai',
            'nippegawai': 'NIP Pegawai',
            'pangkatgolpegawai': 'Pangkat/Golongan Pegawai',
            'jabatanpegawai': 'Jabatan Pegawai',
            'tempattugas': 'Tempat Tugas',
            'kabataukotatujuan': 'Kabupaten/Kota Tujuan',
            'jabatnpegawai': 'Jabatan Pegawai Baru',
            'dd-mm-yyyy': 'Tanggal (dd-mm-yyyy)'
        };
        
        return labelMap[placeholder] || placeholder.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
    };

    if (loading && !templateData) {
        return (
            <div className="flex items-center justify-center p-8">
                <Loader2 className="h-8 w-8 animate-spin" />
                <span className="ml-2">Memuat form...</span>
            </div>
        );
    }

    if (error) {
        return (
            <div className="space-y-4">
                <Button onClick={onBack} variant="outline">
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Kembali
                </Button>
                <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                    <p className="text-red-600">{error}</p>
                </div>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            <div className="flex items-center space-x-4">
                <Button onClick={onBack} variant="outline">
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Kembali
                </Button>
                <div>
                    <h2 className="text-2xl font-bold text-gray-900">{templateData?.name}</h2>
                    <p className="text-gray-600">{templateData?.description}</p>
                </div>
            </div>

            <Card>
                <CardHeader>
                    <CardTitle>Isi Data Surat</CardTitle>
                    <CardDescription>
                        Lengkapi semua field di bawah ini untuk generate surat
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    <form onSubmit={handleSubmit} className="space-y-4">
                        <div className="grid gap-4 md:grid-cols-2">
                            {templateData?.placeholders?.map((placeholder) => (
                                <div key={placeholder} className="space-y-2">
                                    <Label htmlFor={placeholder}>
                                        {getFieldLabel(placeholder)}
                                    </Label>
                                    <Input
                                        id={placeholder}
                                        value={formData[placeholder] || ''}
                                        onChange={(e) => handleInputChange(placeholder, e.target.value)}
                                        placeholder={`Masukkan ${getFieldLabel(placeholder).toLowerCase()}`}
                                        required
                                    />
                                </div>
                            ))}
                        </div>
                        
                        <div className="flex justify-end pt-4">
                            <Button type="submit" disabled={loading}>
                                {loading ? (
                                    <>
                                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                                        Generating...
                                    </>
                                ) : (
                                    <>
                                        <Download className="h-4 w-4 mr-2" />
                                        Generate & Download Surat
                                    </>
                                )}
                            </Button>
                        </div>
                    </form>
                </CardContent>
            </Card>
        </div>
    );
};

export default TemplateForm;
