import axios from 'axios';

const API_BASE_URL = 'http://127.0.0.1:8000/api';

const api = axios.create({
    baseURL: API_BASE_URL,
    headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
    },
});

export const suratApi = {
    // Get all templates
    getTemplates: async () => {
        try {
            const response = await api.get('/templates');
            return response.data;
        } catch (error) {
            throw error.response?.data || error.message;
        }
    },

    // Get template by ID with placeholders
    getTemplate: async (id) => {
        try {
            const response = await api.get(`/templates/${id}`);
            return response.data;
        } catch (error) {
            throw error.response?.data || error.message;
        }
    },

    // Generate document from template
    generateDocument: async (id, data) => {
        try {
            const response = await api.post(`/templates/${id}/generate`, data, {
                responseType: 'blob',
            });
            
            // Create download link
            const url = window.URL.createObjectURL(new Blob([response.data]));
            const link = document.createElement('a');
            link.href = url;
            
            // Get filename from response headers or use default
            const contentDisposition = response.headers['content-disposition'];
            let filename = 'generated_document.docx';
            if (contentDisposition) {
                const filenameMatch = contentDisposition.match(/filename="(.+)"/);
                if (filenameMatch) {
                    filename = filenameMatch[1];
                }
            }
            
            link.setAttribute('download', filename);
            document.body.appendChild(link);
            link.click();
            link.remove();
            window.URL.revokeObjectURL(url);
            
            return { success: true, message: 'Document downloaded successfully' };
        } catch (error) {
            throw error.response?.data || error.message;
        }
    },
};
