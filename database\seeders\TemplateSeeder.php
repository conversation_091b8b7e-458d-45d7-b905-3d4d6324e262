<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Template;

class TemplateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        Template::create([
            'name' => 'Surat Persetujuan Pelepasan Kemenag',
            'filename' => 'KEMENAG_PERSETUJUAN_PELEPASAN.docx',
            'description' => 'Template surat persetujuan pelepasan pegawai Kementerian Agama',
            'placeholders' => [
                'kabkota',
                'jln',
                'telfon',
                'fax',
                'email',
                'namapejabat',
                'nippejabat',
                'pangkatgolpejabat',
                'jabatanpejabat',
                'namapegawai',
                'nippegawai',
                'pangkatgolpegawai',
                'jabatanpegawai',
                'tempattugas',
                'kabataukotatujuan',
                'jabatnpegawai',
                'dd-mm-yyyy'
            ]
        ]);
    }
}
