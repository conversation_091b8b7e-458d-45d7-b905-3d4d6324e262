<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\Storage;

class TemplateController extends Controller
{
    public function index()
    {
        $files = Storage::files('templates');
        $templates = collect($files)->map(function ($file) {
            return [
                'name' => basename($file),
                'path' => $file,
                'id' => pathinfo($file, PATHINFO_FILENAME),
            ];
        });

        return response()->json($templates);
    }

    public function show($id)
    {
        $templatePath = storage_path("app/templates/$id.docx");

        if (!file_exists($templatePath)) {
            return response()->json(['error' => 'Template not found'], 404);
        }

        $placeholders = $this->extractPlaceholders($templatePath);

        return response()->json([
            'id' => $id,
            'placeholders' => $placeholders,
        ]);
    }

    private function extractPlaceholders($path)
    {
        $content = file_get_contents("zip://{$path}#word/document.xml");
        preg_match_all('/{{(.*?)}}/', $content, $matches);
        return array_unique($matches[1]);
    }
}