<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <title>Surat Generator Kemenag</title>

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

        <!-- Styles / Scripts -->
        @if (file_exists(public_path('build/manifest.json')) || file_exists(public_path('hot')))
            @vite(['resources/css/app.css', 'resources/js/app.jsx'])
        @else
            <style>
                /* Basic Tailwind CSS styles for fallback */
                .bg-gray-50 { background-color: #f9fafb; }
                .min-h-screen { min-height: 100vh; }
            </style>
        @endif
    </head>
    <body class="bg-gray-50">
        <div id="app"></div>
    </body>
</html>
