<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Template;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use PhpOffice\PhpWord\TemplateProcessor;
use PhpOffice\PhpWord\IOFactory;

class TemplateController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $templates = Template::all();
        return response()->json([
            'success' => true,
            'data' => $templates
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $template = Template::findOrFail($id);

        // Extract placeholders from the template file if not already stored
        if (!$template->placeholders) {
            $placeholders = $this->extractPlaceholders($template->filename);
            $template->update(['placeholders' => $placeholders]);
        }

        return response()->json([
            'success' => true,
            'data' => $template
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Generate document from template
     */
    public function generate(Request $request, string $id)
    {
        $template = Template::findOrFail($id);

        // Validate request data
        $data = $request->all();

        try {
            // Load template file
            $templatePath = storage_path('app/templates/' . $template->filename);

            if (!file_exists($templatePath)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Template file not found'
                ], 404);
            }

            // Create template processor
            $templateProcessor = new TemplateProcessor($templatePath);

            // Replace placeholders with data
            foreach ($data as $key => $value) {
                $templateProcessor->setValue($key, $value);
            }

            // Generate unique filename
            $generatedFilename = 'generated_' . time() . '_' . $template->filename;
            $generatedPath = storage_path('app/generated/' . $generatedFilename);

            // Save generated file
            $templateProcessor->saveAs($generatedPath);

            // Return file download response
            return response()->download($generatedPath, $generatedFilename, [
                'Content-Type' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
            ])->deleteFileAfterSend(true);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error generating document: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Extract placeholders from template file
     */
    private function extractPlaceholders($filename)
    {
        try {
            $templatePath = storage_path('app/templates/' . $filename);

            if (!file_exists($templatePath)) {
                return [];
            }

            // Read the document.xml from the docx file
            $zip = new \ZipArchive();
            if ($zip->open($templatePath) === TRUE) {
                $documentXml = $zip->getFromName('word/document.xml');
                $zip->close();

                // Extract placeholders using regex
                preg_match_all('/\$\{([^}]+)\}/', $documentXml, $matches);

                return array_unique($matches[1]);
            }

            return [];
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
