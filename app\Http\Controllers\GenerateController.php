<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use PhpOffice\PhpWord\TemplateProcessor;

class GenerateController extends Controller
{
    public function generate(Request $request, $id)
    {
        $templatePath = storage_path("app/templates/$id.docx");

        if (!file_exists($templatePath)) {
            return response()->json(['error' => 'Template not found'], 404);
        }

        $data = $request->all();
        $processor = new TemplateProcessor($templatePath);

        foreach ($data as $key => $value) {
            $processor->setValue($key, $value);
        }

        $outputPath = storage_path('app/generated/' . $id . '_' . now()->timestamp . '.docx');
        $processor->saveAs($outputPath);

        return response()->download($outputPath)->deleteFileAfterSend(true);
    }
}